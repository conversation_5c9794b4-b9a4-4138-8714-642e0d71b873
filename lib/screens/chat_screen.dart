import 'package:flutter/material.dart';
import 'package:stillpoint/services/session_service.dart';
import 'package:stillpoint/services/journal_service.dart';
import 'package:uuid/uuid.dart';

class ChatScreen extends StatefulWidget {
  final CounselorSession session;

  const ChatScreen({super.key, required this.session});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late CounselorSession _currentSession;
  bool _isLoading = false;
  late AnimationController _typingAnimationController;
  late List<Animation<double>> _dotAnimations;

  @override
  void initState() {
    super.initState();
    _currentSession = widget.session;

    // Initialize typing animation
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Create staggered animations for 3 dots
    _dotAnimations = List.generate(3, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _typingAnimationController,
          curve: Interval(
            index * 0.2,
            0.6 + (index * 0.2),
            curve: Curves.easeInOut,
          ),
        ),
      );
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
      // Generate initial welcome message with typing animation
      _generateInitialWelcome();
    });
  }

  Future<void> _generateInitialWelcome() async {
    // Only generate if there's only the system message
    if (_currentSession.messages.length <= 1) {
      setState(() {
        _isLoading = true;
      });

      // Start typing animation
      _typingAnimationController.repeat();

      try {
        final updatedSession = await SessionService.generateInitialWelcome(
          _currentSession,
        );

        setState(() {
          _currentSession = updatedSession;
          _isLoading = false;
        });

        // Stop typing animation
        _typingAnimationController.stop();
        _typingAnimationController.reset();

        _scrollToBottom();
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        _typingAnimationController.stop();
        _typingAnimationController.reset();
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isLoading) return;

    _messageController.clear();

    // Add user message immediately to UI
    final userMessage = ChatMessage(
      id: const Uuid().v4(),
      content: message,
      isUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _currentSession = _currentSession.copyWith(
        messages: [..._currentSession.messages, userMessage],
      );
      _isLoading = true;
    });

    // Start typing animation
    _typingAnimationController.repeat();
    _scrollToBottom();

    try {
      // Process the message and get counselor response
      // SessionService.addMessage will handle adding the AI response
      final updatedSession = await SessionService.addMessage(
        _currentSession,
        message,
        true,
      );

      setState(() {
        _currentSession = updatedSession;
        _isLoading = false;
      });

      // Stop typing animation
      _typingAnimationController.stop();
      _typingAnimationController.reset();

      _scrollToBottom();

      // Check if session should be completed (reached 10 questions)
      // The new flow is handled automatically by the session service
      // when the AI asks about journaling prompts
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // Stop typing animation on error
      _typingAnimationController.stop();
      _typingAnimationController.reset();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveAndExit() async {
    try {
      await SessionService.pauseSession(_currentSession);
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleJournalingResponse(bool wantsPrompts) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final updatedSession = await SessionService.handleJournalingResponse(
        _currentSession,
        wantsPrompts,
      );

      setState(() {
        _currentSession = updatedSession;
        _isLoading = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process response: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addPromptsToJournal() async {
    try {
      final updatedSession = await SessionService.addPromptsToJournal(
        _currentSession,
      );

      setState(() {
        _currentSession = updatedSession;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add prompts to journal: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeSessionNew() async {
    try {
      final completedSession = await SessionService.completeSessionAfterPrompts(
        _currentSession,
      );

      setState(() {
        _currentSession = completedSession;
      });

      if (mounted) {
        Navigator.pop(context); // Return to previous screen
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addPromptsToJournalOld(List<String> prompts) async {
    try {
      await JournalService.createFromSessionPrompts(
        sessionId: _currentSession.id,
        counselorName: _currentSession.counselorName,
        sessionTitle: _currentSession.displayTitle,
        prompts: prompts,
      );

      if (mounted) {
        Navigator.pop(context); // Close dialog
        Navigator.pop(context); // Return to previous screen

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Journaling prompts added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add prompts to journal: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildInputArea(ColorScheme colorScheme, Color counselorColor) {
    // Handle different end states
    switch (_currentSession.endState) {
      case SessionEndState.awaitingJournalingResponse:
        return _buildJournalingResponseArea(colorScheme, counselorColor);
      case SessionEndState.generatingPrompts:
        return _buildGeneratingPromptsArea(colorScheme, counselorColor);
      case SessionEndState.promptsGenerated:
        return _buildPromptsGeneratedArea(colorScheme, counselorColor);
      case SessionEndState.addingPromptsToJournal:
        return _buildAddingPromptsArea(colorScheme, counselorColor);
      case SessionEndState.promptsAddedToJournal:
        return _buildPromptsAddedArea(colorScheme, counselorColor);
      case SessionEndState.sessionEnding:
        return _buildSessionEndingArea(colorScheme, counselorColor);
      case SessionEndState.none:
        break;
    }

    // Check if session is completed (old flow)
    if (_currentSession.isCompleted) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
          ),
        ),
        child: Column(
          children: [
            // Disabled input field
            TextField(
              enabled: false,
              decoration: InputDecoration(
                hintText: 'Session complete - come back tomorrow to continue',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.1,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 12),
            // Add prompts to journal button
            if (_currentSession.journalingPrompts != null)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed:
                      () => _addPromptsToJournalOld(
                        _currentSession.journalingPrompts!,
                      ),
                  icon: const Icon(Icons.book_outlined),
                  label: const Text('Add Prompts to Journal'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: counselorColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // Regular input area for active sessions
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Share what\'s on your mind...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: colorScheme.surfaceContainerHighest.withValues(
                  alpha: 0.3,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
              onSubmitted: (_) => _sendMessage(),
              enabled: !_isLoading,
            ),
          ),
          const SizedBox(width: 12),
          FloatingActionButton.small(
            onPressed: _isLoading ? null : _sendMessage,
            backgroundColor: counselorColor,
            child:
                _isLoading
                    ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.send, color: Colors.white),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final counselorColor = Color(_currentSession.counselorColor);

    return PopScope(
      canPop: false, // Prevent back navigation
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _showExitConfirmationDialog();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: counselorColor,
          foregroundColor: Colors.white,
          automaticallyImplyLeading: false, // Remove back button
          title: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(14),
                  child: Image.asset(
                    _currentSession.counselorImagePath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(14),
                        ),
                        child: Center(
                          child: Text(
                            _currentSession.counselorImage,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentSession.counselorName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Online now',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // Save and Exit button
            TextButton.icon(
              onPressed: _saveAndExit,
              icon: const Icon(
                Icons.save_outlined,
                color: Colors.white,
                size: 18,
              ),
              label: const Text(
                'Save & Exit',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
            // End Session button
            TextButton.icon(
              onPressed: _showEndSessionDialog,
              icon: const Icon(
                Icons.stop_outlined,
                color: Colors.white,
                size: 18,
              ),
              label: const Text(
                'End Session',
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            // Chat messages
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      counselorColor.withValues(alpha: 0.02),
                      colorScheme.surface,
                    ],
                  ),
                ),
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount:
                      _currentSession.messages.length + (_isLoading ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _currentSession.messages.length &&
                        _isLoading) {
                      return _buildTypingIndicator(counselorColor);
                    }
                    return _buildMessageBubble(
                      _currentSession.messages[index],
                      counselorColor,
                      theme,
                    );
                  },
                ),
              ),
            ),

            // Input area or session completion area
            _buildInputArea(colorScheme, counselorColor),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(
    ChatMessage message,
    Color counselorColor,
    ThemeData theme,
  ) {
    final colorScheme = theme.colorScheme;

    // Check if this is a system message (first message with session info)
    final isSystemMessage =
        !message.isUser && message.content == 'Session Information';

    if (isSystemMessage) {
      return Container(
        margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16, top: 8),
        child: Card(
          elevation: 0,
          color: colorScheme.surfaceContainerLow,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with icon
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.info_outline,
                        size: 20,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Session Information',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Content sections
                _buildInfoSection(
                  'How this works:',
                  'Our conversation follows a structured approach - I\'ll listen to your feelings, help you analyze patterns, and ask thoughtful questions to deepen your understanding. We\'ll work through 8-10 exchanges to explore your concerns thoroughly.',
                  theme,
                  colorScheme,
                ),
                const SizedBox(height: 12),

                _buildInfoSection(
                  'For the best experience:',
                  'Share openly and give detailed responses rather than brief answers. The more context you provide, the more meaningful insights we can discover together.',
                  theme,
                  colorScheme,
                ),
                const SizedBox(height: 12),

                _buildInfoSection(
                  'Privacy & Confidentiality:',
                  'All conversations are completely private and confidential, except in cases where there\'s a suspected emergency or immediate safety concern. For full details, see our Privacy Policy.',
                  theme,
                  colorScheme,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: counselorColor, width: 2),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(14),
                child: Image.asset(
                  _currentSession.counselorImagePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      decoration: BoxDecoration(
                        color: counselorColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: Center(
                        child: Text(
                          _currentSession.counselorImage,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color:
                    message.isUser
                        ? counselorColor
                        : colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(18),
              ),
              child: Text(
                message.content,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: message.isUser ? Colors.white : colorScheme.onSurface,
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: colorScheme.secondary,
              child: const Icon(Icons.person, size: 16, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoSection(
    String title,
    String content,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurfaceVariant,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildTypingIndicator(Color counselorColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: counselorColor, width: 2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Image.asset(
                _currentSession.counselorImagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: BoxDecoration(
                      color: counselorColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Center(
                      child: Text(
                        _currentSession.counselorImage,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(18),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated typing dots
                Row(
                  children: List.generate(3, (index) {
                    return AnimatedBuilder(
                      animation: _dotAnimations[index],
                      builder: (context, child) {
                        return Container(
                          margin: EdgeInsets.only(
                            right: index < 2 ? 4 : 0,
                            bottom: _dotAnimations[index].value * 4,
                          ),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: counselorColor.withValues(
                              alpha: 0.3 + (_dotAnimations[index].value * 0.7),
                            ),
                            shape: BoxShape.circle,
                          ),
                        );
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showExitConfirmationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Leave Session?'),
            content: const Text(
              'Are you sure you want to leave this session? Your progress will be saved, but you\'ll need to start a new session to continue.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Stay'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  _saveAndExit(); // Save and exit
                },
                child: const Text('Save & Exit'),
              ),
            ],
          ),
    );
  }

  void _showEndSessionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('End Session'),
            content: const Text(
              'Are you sure you want to end this session? Your conversation will be saved.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Close chat screen
                },
                child: const Text('End Session'),
              ),
            ],
          ),
    );
  }

  /// Build UI when awaiting journaling response (Yes/No buttons)
  Widget _buildJournalingResponseArea(
    ColorScheme colorScheme,
    Color counselorColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Waiting for your response...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Yes/No buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _handleJournalingResponse(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: counselorColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text('Yes'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _handleJournalingResponse(false),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: counselorColor,
                    side: BorderSide(color: counselorColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text('No'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build UI when generating prompts (loading state)
  Widget _buildGeneratingPromptsArea(
    ColorScheme colorScheme,
    Color counselorColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Generating journaling prompts...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Loading indicator
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(counselorColor),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Creating personalized prompts...',
                style: TextStyle(
                  color: colorScheme.onSurfaceVariant,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build UI when prompts are generated (Add to Journal / Complete Session buttons)
  Widget _buildPromptsGeneratedArea(
    ColorScheme colorScheme,
    Color counselorColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Session complete',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _addPromptsToJournal,
                  icon: const Icon(Icons.book_outlined),
                  label: const Text('Add prompts to journal'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: counselorColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: _completeSessionNew,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: counselorColor,
                    side: BorderSide(color: counselorColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text('Complete session'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Your session notes have been added to your journal.',
            style: TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build UI when adding prompts to journal (loading state)
  Widget _buildAddingPromptsArea(
    ColorScheme colorScheme,
    Color counselorColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Adding prompts to journal...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Loading indicator
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(counselorColor),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Adding prompts to your journal...',
                style: TextStyle(
                  color: colorScheme.onSurfaceVariant,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build UI when prompts have been added to journal
  Widget _buildPromptsAddedArea(ColorScheme colorScheme, Color counselorColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Session complete',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Greyed out "prompts added" button and complete session button
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: null, // Disabled
                  icon: const Icon(Icons.check),
                  label: const Text('Prompts added to journal!'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.withValues(alpha: 0.3),
                    foregroundColor: Colors.grey,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _completeSessionNew,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: counselorColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text('Complete session'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Your session notes have been added to your journal.',
            style: TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build UI when session is ending (single Complete Session button)
  Widget _buildSessionEndingArea(
    ColorScheme colorScheme,
    Color counselorColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Greyed out input field
          TextField(
            enabled: false,
            decoration: InputDecoration(
              hintText: 'Session complete',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.1,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Single complete session button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _completeSessionNew,
              style: ElevatedButton.styleFrom(
                backgroundColor: counselorColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text(
                'Complete session',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your session notes have been added to your journal.',
            style: TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
