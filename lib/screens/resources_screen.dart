import 'package:flutter/material.dart';
import 'package:stillpoint/services/user_profile_service.dart';

class ResourcesScreen extends StatefulWidget {
  const ResourcesScreen({super.key});

  @override
  State<ResourcesScreen> createState() => _ResourcesScreenState();
}

class _ResourcesScreenState extends State<ResourcesScreen> {
  List<String> _selectedAreas = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserFocusAreas();
  }

  Future<void> _loadUserFocusAreas() async {
    try {
      final focusAreas = await UserProfileService.getFocusAreas();
      if (mounted) {
        setState(() {
          _selectedAreas = focusAreas;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Resources'),
        backgroundColor: colorScheme.tertiary,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false, // Remove back button
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.tertiary.withValues(alpha: 0.05),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: colorScheme.surface,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.library_books_outlined,
                                size: 64,
                                color: colorScheme.tertiary,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Personalized Resources',
                                style: theme.textTheme.headlineMedium?.copyWith(
                                  color: colorScheme.tertiary,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Curated blogs, worksheets, and tools based on your focus areas.',
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              if (_selectedAreas.isNotEmpty) ...[
                                Text(
                                  'Your Focus Areas:',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color: colorScheme.tertiary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Wrap(
                                  spacing: 8,
                                  children:
                                      _selectedAreas.map((area) {
                                        return Chip(
                                          label: Text(area),
                                          backgroundColor: colorScheme.tertiary
                                              .withValues(alpha: 0.1),
                                          labelStyle: TextStyle(
                                            color: colorScheme.tertiary,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        );
                                      }).toList(),
                                ),
                                const SizedBox(height: 16),
                              ],
                              Text(
                                'Coming Soon!',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: colorScheme.secondary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ),
      ),
    )
  }
}
